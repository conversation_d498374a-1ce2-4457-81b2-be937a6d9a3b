/**
 * Firestore集成测试
 * 测试Firestore数据库功能是否正常工作
 */

import { databaseAPI } from '../utils/firestoreDatabaseAPI.js';
import { firestoreLearningManager } from '../utils/firestoreLearningManager.js';
import { checkFirebaseConnection } from '../utils/firebase.js';

/**
 * 测试套件类
 */
export class FirestoreTestSuite {
  constructor() {
    this.testResults = [];
    this.isRunning = false;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    if (this.isRunning) {
      console.warn('测试已在运行中');
      return;
    }

    this.isRunning = true;
    this.testResults = [];
    
    console.log('🚀 开始Firestore集成测试...');
    
    try {
      // 基础连接测试
      await this.testFirebaseConnection();
      
      // 数据库API测试
      await this.testDatabaseAPI();
      
      // 句子CRUD测试
      await this.testSentenceCRUD();
      
      // 学习记录测试
      await this.testLearningRecords();
      
      // 学习管理器测试
      await this.testLearningManager();
      
      // 用户偏好测试
      await this.testUserPreferences();
      
      // 输出测试结果
      this.printTestResults();
      
    } catch (error) {
      console.error('❌ 测试运行失败:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 测试Firebase连接
   */
  async testFirebaseConnection() {
    try {
      const isConnected = await checkFirebaseConnection();
      this.addTestResult('Firebase连接测试', isConnected, isConnected ? '连接成功' : '连接失败');
    } catch (error) {
      this.addTestResult('Firebase连接测试', false, `连接错误: ${error.message}`);
    }
  }

  /**
   * 测试数据库API初始化
   */
  async testDatabaseAPI() {
    try {
      const initialized = await databaseAPI.initialize();
      this.addTestResult('数据库API初始化', initialized, initialized ? '初始化成功' : '初始化失败');
    } catch (error) {
      this.addTestResult('数据库API初始化', false, `初始化错误: ${error.message}`);
    }
  }

  /**
   * 测试句子CRUD操作
   */
  async testSentenceCRUD() {
    try {
      // 测试数据
      const testSentence = {
        english: "This is a test sentence.",
        chinese: "这是一个测试句子。",
        scene: "test",
        difficulty: "beginner",
        tags: ["test"],
        explanation: "测试用句子"
      };

      // 测试添加句子
      const sentenceId = await databaseAPI.addSentence(testSentence);
      this.addTestResult('添加句子', !!sentenceId, sentenceId ? `句子ID: ${sentenceId}` : '添加失败');

      if (sentenceId) {
        // 测试获取句子
        const retrievedSentence = await databaseAPI.getSentenceById(sentenceId);
        const getSuccess = retrievedSentence && retrievedSentence.english === testSentence.english;
        this.addTestResult('获取句子', getSuccess, getSuccess ? '获取成功' : '获取失败');

        // 测试更新句子
        const updateData = { explanation: "更新后的解释" };
        const updateSuccess = await databaseAPI.updateSentence(sentenceId, updateData);
        this.addTestResult('更新句子', updateSuccess, updateSuccess ? '更新成功' : '更新失败');

        // 测试搜索句子
        const searchResults = await databaseAPI.searchSentences("test");
        const searchSuccess = searchResults.length > 0;
        this.addTestResult('搜索句子', searchSuccess, `找到 ${searchResults.length} 个结果`);

        // 测试删除句子（清理测试数据）
        const deleteSuccess = await databaseAPI.deleteSentence(sentenceId);
        this.addTestResult('删除句子', deleteSuccess, deleteSuccess ? '删除成功' : '删除失败');
      }
    } catch (error) {
      this.addTestResult('句子CRUD测试', false, `测试错误: ${error.message}`);
    }
  }

  /**
   * 测试学习记录功能
   */
  async testLearningRecords() {
    try {
      // 测试记录学习活动
      const testRecord = {
        sentenceId: 'test-sentence-id',
        isCorrect: true,
        responseTime: 2000,
        masteryLevel: 2,
        memoryStrength: 0.8
      };

      const recordId = await databaseAPI.recordLearningActivity(testRecord);
      this.addTestResult('记录学习活动', !!recordId, recordId ? `记录ID: ${recordId}` : '记录失败');

      if (recordId) {
        // 测试获取学习记录
        const retrievedRecord = await databaseAPI.getLearningRecord(testRecord.sentenceId);
        const getSuccess = retrievedRecord && retrievedRecord.sentenceId === testRecord.sentenceId;
        this.addTestResult('获取学习记录', getSuccess, getSuccess ? '获取成功' : '获取失败');

        // 测试更新学习记录
        const updateData = { masteryLevel: 3 };
        const updateSuccess = await databaseAPI.updateLearningRecord(testRecord.sentenceId, updateData);
        this.addTestResult('更新学习记录', updateSuccess, updateSuccess ? '更新成功' : '更新失败');
      }
    } catch (error) {
      this.addTestResult('学习记录测试', false, `测试错误: ${error.message}`);
    }
  }

  /**
   * 测试学习管理器
   */
  async testLearningManager() {
    try {
      // 测试初始化
      const initialized = await firestoreLearningManager.initialize();
      this.addTestResult('学习管理器初始化', initialized, initialized ? '初始化成功' : '初始化失败');

      if (initialized) {
        // 测试记录活动
        const activity = {
          sentenceId: 'test-sentence-id-2',
          isCorrect: true,
          responseTime: 1500,
          difficulty: 'beginner'
        };

        const recordId = await firestoreLearningManager.recordActivity(activity);
        this.addTestResult('学习管理器记录活动', !!recordId, recordId ? '记录成功' : '记录失败');

        // 测试获取学习数据
        const learningData = await firestoreLearningManager.getLearningData(activity.sentenceId);
        const dataSuccess = learningData && learningData.sentenceId === activity.sentenceId;
        this.addTestResult('获取学习数据', dataSuccess, dataSuccess ? '获取成功' : '获取失败');

        // 测试获取统计信息
        const stats = await firestoreLearningManager.getStatistics();
        const statsSuccess = stats && typeof stats === 'object';
        this.addTestResult('获取学习统计', statsSuccess, statsSuccess ? '获取成功' : '获取失败');
      }
    } catch (error) {
      this.addTestResult('学习管理器测试', false, `测试错误: ${error.message}`);
    }
  }

  /**
   * 测试用户偏好功能
   */
  async testUserPreferences() {
    try {
      // 测试设置偏好
      const testKey = 'test_preference';
      const testValue = { theme: 'dark', language: 'zh' };
      
      const setSuccess = await databaseAPI.setUserPreference(testKey, testValue);
      this.addTestResult('设置用户偏好', setSuccess, setSuccess ? '设置成功' : '设置失败');

      if (setSuccess) {
        // 测试获取偏好
        const retrievedValue = await databaseAPI.getUserPreference(testKey);
        const getSuccess = retrievedValue && retrievedValue.theme === testValue.theme;
        this.addTestResult('获取用户偏好', getSuccess, getSuccess ? '获取成功' : '获取失败');

        // 测试获取所有偏好
        const allPreferences = await databaseAPI.getAllUserPreferences();
        const allSuccess = allPreferences && typeof allPreferences === 'object';
        this.addTestResult('获取所有偏好', allSuccess, allSuccess ? '获取成功' : '获取失败');
      }
    } catch (error) {
      this.addTestResult('用户偏好测试', false, `测试错误: ${error.message}`);
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      name: testName,
      success,
      message,
      timestamp: new Date()
    });
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('\n📊 Firestore集成测试结果:');
    console.log('=' * 50);
    
    let passedCount = 0;
    let failedCount = 0;
    
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅ 通过' : '❌ 失败';
      const icon = result.success ? '✅' : '❌';
      
      console.log(`${index + 1}. ${result.name}: ${status}`);
      console.log(`   ${result.message}`);
      console.log('');
      
      if (result.success) {
        passedCount++;
      } else {
        failedCount++;
      }
    });
    
    console.log('=' * 50);
    console.log(`📈 测试总结: ${passedCount} 通过, ${failedCount} 失败`);
    
    if (failedCount === 0) {
      console.log('🎉 所有测试通过！Firestore集成成功！');
    } else {
      console.log('⚠️  部分测试失败，请检查Firestore配置和网络连接');
    }
  }

  /**
   * 获取测试结果
   */
  getTestResults() {
    return {
      results: this.testResults,
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.success).length,
        failed: this.testResults.filter(r => !r.success).length
      }
    };
  }
}

// 导出测试套件实例
export const firestoreTestSuite = new FirestoreTestSuite();

// 如果直接运行此文件，执行测试
// 注意：在浏览器环境中，我们不能使用process.argv
// 如果需要自动运行测试，可以在控制台手动调用 firestoreTestSuite.runAllTests()
if (typeof process !== 'undefined' && import.meta.url === `file://${process.argv[1]}`) {
  firestoreTestSuite.runAllTests();
}
