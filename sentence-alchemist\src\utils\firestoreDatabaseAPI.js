/**
 * Firestore数据库API层
 * 提供统一的数据访问接口，封装所有Firestore数据库操作
 * 替换原有的SQLite数据库API
 */

import { firestoreManager } from './firestoreDatabase.js';
import { checkFirebaseConnection } from './firebase.js';

/**
 * Firestore数据库API类
 */
export class FirestoreDatabaseAPI {
  constructor() {
    this.db = firestoreManager;
    this.isReady = false;
  }

  /**
   * 初始化数据库API
   */
  async initialize() {
    try {
      // 检查Firebase连接
      const isConnected = await checkFirebaseConnection();
      if (!isConnected) {
        throw new Error('Firebase连接失败');
      }
      
      // 初始化Firestore管理器
      await this.db.initialize();
      
      this.isReady = true;
      console.log('Firestore数据库API初始化完成');
      return true;
    } catch (error) {
      console.error('Firestore数据库API初始化失败:', error);
      return false;
    }
  }

  /**
   * 检查API是否就绪
   */
  checkReady() {
    if (!this.isReady) {
      throw new Error('Firestore数据库API未初始化，请先调用initialize()');
    }
  }

  // ==================== 句子相关API ====================

  /**
   * 获取所有句子
   * @param {Object} options - 查询选项
   * @returns {Array} 句子数组
   */
  async getSentences(options = {}) {
    this.checkReady();
    return this.db.getAllSentences(options);
  }

  /**
   * 根据ID获取句子
   * @param {string} id - 句子ID
   * @returns {Object|null} 句子对象
   */
  async getSentenceById(id) {
    this.checkReady();
    return this.db.getSentenceById(id);
  }

  /**
   * 搜索句子
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Array} 搜索结果
   */
  async searchSentences(keyword, options = {}) {
    this.checkReady();
    return this.db.searchSentences(keyword, options);
  }

  /**
   * 获取随机句子
   * @param {Object} options - 选项
   * @returns {Object|null} 随机句子
   */
  async getRandomSentence(options = {}) {
    this.checkReady();
    return this.db.getRandomSentence(options);
  }

  /**
   * 添加句子
   * @param {Object} sentence - 句子对象
   * @returns {string} 新增句子的ID
   */
  async addSentence(sentence) {
    this.checkReady();
    return this.db.addSentence(sentence);
  }

  /**
   * 更新句子
   * @param {string} id - 句子ID
   * @param {Object} updates - 更新数据
   * @returns {boolean} 是否更新成功
   */
  async updateSentence(id, updates) {
    this.checkReady();
    return this.db.updateSentence(id, updates);
  }

  /**
   * 删除句子
   * @param {string} id - 句子ID
   * @returns {boolean} 是否删除成功
   */
  async deleteSentence(id) {
    this.checkReady();
    return this.db.deleteSentence(id);
  }

  // ==================== 学习记录相关API ====================

  /**
   * 记录学习活动
   * @param {Object} record - 学习记录
   * @returns {string} 记录ID
   */
  async recordLearningActivity(record) {
    this.checkReady();
    return this.db.recordLearningActivity(record);
  }

  /**
   * 获取句子的学习记录
   * @param {string} sentenceId - 句子ID
   * @returns {Object|null} 学习记录
   */
  async getLearningRecord(sentenceId) {
    this.checkReady();
    return this.db.getLearningRecord(sentenceId);
  }

  /**
   * 更新学习记录
   * @param {string} sentenceId - 句子ID
   * @param {Object} updates - 更新数据
   * @returns {boolean} 是否更新成功
   */
  async updateLearningRecord(sentenceId, updates) {
    this.checkReady();
    return this.db.updateLearningRecord(sentenceId, updates);
  }

  // ==================== 用户偏好相关API ====================

  /**
   * 获取用户偏好
   * @param {string} key - 偏好键
   * @returns {any} 偏好值
   */
  async getUserPreference(key) {
    this.checkReady();
    return this.db.getUserPreference(key);
  }

  /**
   * 设置用户偏好
   * @param {string} key - 偏好键
   * @param {any} value - 偏好值
   * @returns {boolean} 是否设置成功
   */
  async setUserPreference(key, value) {
    this.checkReady();
    return this.db.setUserPreference(key, value);
  }

  /**
   * 获取所有用户偏好
   * @returns {Object} 偏好对象
   */
  async getAllUserPreferences() {
    this.checkReady();
    return this.db.getAllUserPreferences();
  }

  // ==================== 统计相关API ====================

  /**
   * 获取学习统计
   * @returns {Object} 统计信息
   */
  async getLearningStatistics() {
    this.checkReady();
    return this.db.getLearningStatistics();
  }

  /**
   * 获取数据库统计信息
   * @returns {Object} 数据库统计
   */
  async getDatabaseStatistics() {
    this.checkReady();
    return this.db.getStats();
  }

  // ==================== 工具方法 ====================

  /**
   * 清空所有数据
   */
  async clearAllData() {
    this.checkReady();
    return this.db.clearAllData();
  }

  /**
   * 批量操作
   * @param {Function} operations - 批量操作函数
   * @returns {Promise} 操作结果
   */
  async batchOperation(operations) {
    this.checkReady();
    return this.db.batchOperation(operations);
  }

  /**
   * 事务操作
   * @param {Function} operations - 事务操作函数
   * @returns {Promise} 操作结果
   */
  async transactionOperation(operations) {
    this.checkReady();
    return this.db.transactionOperation(operations);
  }

  /**
   * 备份数据（Firestore自动备份，这里提供兼容性接口）
   */
  async backupToStorage() {
    // Firestore自动处理备份，这里只是为了兼容性
    console.log('Firestore自动处理数据备份');
    return true;
  }

  /**
   * 导出数据库（提供兼容性接口）
   */
  async exportDatabase() {
    this.checkReady();
    
    try {
      // 导出所有句子数据
      const sentences = await this.getSentences();
      const preferences = await this.getAllUserPreferences();
      const stats = await this.getDatabaseStatistics();
      
      return {
        sentences,
        preferences,
        stats,
        exportedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('导出数据失败:', error);
      throw error;
    }
  }

  /**
   * 驼峰命名转下划线命名（兼容性方法）
   * @param {string} str - 驼峰命名字符串
   * @returns {string} 下划线命名字符串
   */
  camelToSnake(str) {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}

// 导出单例实例
export const databaseAPI = new FirestoreDatabaseAPI();
