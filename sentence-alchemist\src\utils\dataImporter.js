/**
 * 数据导入工具
 * 支持批量导入句子、俚语、单词等数据到Firestore
 */

import { databaseAPI } from './firestoreDatabaseAPI.js';
import { 
  collection, 
  writeBatch, 
  doc,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from './firebase.js';
import { COLLECTIONS } from './firestoreSchema.js';

/**
 * 数据导入器类
 */
export class DataImporter {
  constructor() {
    this.batchSize = 500; // Firestore批量操作限制
    this.importStats = {
      sentences: 0,
      words: 0,
      idioms: 0,
      errors: 0
    };
  }

  /**
   * 导入句子数据
   * @param {Array} sentences - 句子数组
   * @returns {Promise<Object>} 导入结果
   */
  async importSentences(sentences) {
    console.log(`开始导入 ${sentences.length} 个句子...`);
    
    try {
      // 确保数据库已初始化
      if (!databaseAPI.isReady) {
        await databaseAPI.initialize();
      }

      const batches = this.createBatches(sentences, this.batchSize);
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`处理批次 ${i + 1}/${batches.length} (${batch.length} 个句子)`);

        try {
          await this.importSentenceBatch(batch);
          successCount += batch.length;
          console.log(`✅ 批次 ${i + 1} 导入成功`);
        } catch (error) {
          console.error(`❌ 批次 ${i + 1} 导入失败:`, error);
          errorCount += batch.length;
        }

        // 添加延迟避免超出配额
        if (i < batches.length - 1) {
          await this.delay(100);
        }
      }

      this.importStats.sentences = successCount;
      this.importStats.errors += errorCount;

      return {
        success: true,
        imported: successCount,
        errors: errorCount,
        total: sentences.length
      };
    } catch (error) {
      console.error('导入句子失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 导入单个句子批次
   * @param {Array} sentences - 句子批次
   */
  async importSentenceBatch(sentences) {
    const batch = writeBatch(db);
    const sentencesRef = collection(db, COLLECTIONS.SENTENCES);

    sentences.forEach((sentence) => {
      // 验证和清理数据
      const cleanSentence = this.validateAndCleanSentence(sentence);
      if (cleanSentence) {
        const docRef = doc(sentencesRef);
        batch.set(docRef, {
          ...cleanSentence,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          totalAttempts: 0,
          correctAttempts: 0,
          averageResponseTime: 0
        });
      }
    });

    await batch.commit();
  }

  /**
   * 验证和清理句子数据
   * @param {Object} sentence - 原始句子数据
   * @returns {Object|null} 清理后的句子数据
   */
  validateAndCleanSentence(sentence) {
    try {
      // 必需字段检查
      if (!sentence.english || !sentence.chinese) {
        console.warn('跳过无效句子：缺少英文或中文', sentence);
        return null;
      }

      return {
        english: String(sentence.english).trim(),
        chinese: String(sentence.chinese).trim(),
        pronunciation: sentence.pronunciation ? String(sentence.pronunciation).trim() : '',
        chinesePronunciation: sentence.chinesePronunciation ? String(sentence.chinesePronunciation).trim() : '',
        scene: sentence.scene ? String(sentence.scene).trim() : 'general',
        difficulty: sentence.difficulty ? String(sentence.difficulty).trim() : 'beginner',
        tags: Array.isArray(sentence.tags) ? sentence.tags : [],
        explanation: sentence.explanation ? String(sentence.explanation).trim() : '',
        grammarPoints: Array.isArray(sentence.grammarPoints) ? sentence.grammarPoints : []
      };
    } catch (error) {
      console.warn('清理句子数据失败:', error, sentence);
      return null;
    }
  }

  /**
   * 导入俚语数据
   * @param {Array} idioms - 俚语数组
   * @returns {Promise<Object>} 导入结果
   */
  async importIdioms(idioms) {
    console.log(`开始导入 ${idioms.length} 个俚语...`);
    
    try {
      const batches = this.createBatches(idioms, this.batchSize);
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`处理俚语批次 ${i + 1}/${batches.length}`);

        try {
          await this.importIdiomBatch(batch);
          successCount += batch.length;
        } catch (error) {
          console.error(`俚语批次 ${i + 1} 导入失败:`, error);
          errorCount += batch.length;
        }

        await this.delay(100);
      }

      this.importStats.idioms = successCount;
      return {
        success: true,
        imported: successCount,
        errors: errorCount,
        total: idioms.length
      };
    } catch (error) {
      console.error('导入俚语失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 导入俚语批次
   * @param {Array} idioms - 俚语批次
   */
  async importIdiomBatch(idioms) {
    const batch = writeBatch(db);
    const idiomsRef = collection(db, COLLECTIONS.IDIOMS);

    idioms.forEach((idiom) => {
      const cleanIdiom = this.validateAndCleanIdiom(idiom);
      if (cleanIdiom) {
        const docRef = doc(idiomsRef);
        batch.set(docRef, {
          ...cleanIdiom,
          createdAt: serverTimestamp()
        });
      }
    });

    await batch.commit();
  }

  /**
   * 验证和清理俚语数据
   * @param {Object} idiom - 原始俚语数据
   * @returns {Object|null} 清理后的俚语数据
   */
  validateAndCleanIdiom(idiom) {
    try {
      if (!idiom.english || !idiom.chinese) {
        return null;
      }

      return {
        english: String(idiom.english).trim(),
        chinese: String(idiom.chinese).trim(),
        pronunciation: idiom.pronunciation ? String(idiom.pronunciation).trim() : '',
        meaning: idiom.meaning ? String(idiom.meaning).trim() : '',
        usageExample: idiom.usageExample ? String(idiom.usageExample).trim() : '',
        origin: idiom.origin ? String(idiom.origin).trim() : '',
        difficulty: idiom.difficulty ? String(idiom.difficulty).trim() : 'intermediate',
        tags: Array.isArray(idiom.tags) ? idiom.tags : []
      };
    } catch (error) {
      console.warn('清理俚语数据失败:', error);
      return null;
    }
  }

  /**
   * 导入单词数据
   * @param {Array} words - 单词数组
   * @returns {Promise<Object>} 导入结果
   */
  async importWords(words) {
    console.log(`开始导入 ${words.length} 个单词...`);
    
    try {
      const batches = this.createBatches(words, this.batchSize);
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`处理单词批次 ${i + 1}/${batches.length}`);

        try {
          await this.importWordBatch(batch);
          successCount += batch.length;
        } catch (error) {
          console.error(`单词批次 ${i + 1} 导入失败:`, error);
          errorCount += batch.length;
        }

        await this.delay(100);
      }

      this.importStats.words = successCount;
      return {
        success: true,
        imported: successCount,
        errors: errorCount,
        total: words.length
      };
    } catch (error) {
      console.error('导入单词失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 导入单词批次
   * @param {Array} words - 单词批次
   */
  async importWordBatch(words) {
    const batch = writeBatch(db);
    const wordsRef = collection(db, COLLECTIONS.WORDS);

    words.forEach((word) => {
      const cleanWord = this.validateAndCleanWord(word);
      if (cleanWord) {
        const docRef = doc(wordsRef);
        batch.set(docRef, {
          ...cleanWord,
          createdAt: serverTimestamp()
        });
      }
    });

    await batch.commit();
  }

  /**
   * 验证和清理单词数据
   * @param {Object} word - 原始单词数据
   * @returns {Object|null} 清理后的单词数据
   */
  validateAndCleanWord(word) {
    try {
      if (!word.word || !word.meaning) {
        return null;
      }

      return {
        word: String(word.word).trim(),
        pronunciation: word.pronunciation ? String(word.pronunciation).trim() : '',
        partOfSpeech: word.partOfSpeech ? String(word.partOfSpeech).trim() : '',
        meaning: String(word.meaning).trim(),
        usage: word.usage ? String(word.usage).trim() : '',
        exampleSentences: Array.isArray(word.exampleSentences) ? word.exampleSentences : [],
        frequency: word.frequency ? Number(word.frequency) : 0
      };
    } catch (error) {
      console.warn('清理单词数据失败:', error);
      return null;
    }
  }

  /**
   * 从JSON文件导入数据
   * @param {File} file - JSON文件
   * @param {string} dataType - 数据类型 ('sentences', 'idioms', 'words')
   * @returns {Promise<Object>} 导入结果
   */
  async importFromFile(file, dataType) {
    try {
      const text = await file.text();
      const data = JSON.parse(text);
      
      if (!Array.isArray(data)) {
        throw new Error('文件格式错误：数据应该是数组格式');
      }

      switch (dataType) {
        case 'sentences':
          return await this.importSentences(data);
        case 'idioms':
          return await this.importIdioms(data);
        case 'words':
          return await this.importWords(data);
        default:
          throw new Error(`不支持的数据类型: ${dataType}`);
      }
    } catch (error) {
      console.error('从文件导入失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建批次
   * @param {Array} array - 原数组
   * @param {number} size - 批次大小
   * @returns {Array} 批次数组
   */
  createBatches(array, size) {
    const batches = [];
    for (let i = 0; i < array.length; i += size) {
      batches.push(array.slice(i, i + size));
    }
    return batches;
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取导入统计
   * @returns {Object} 统计信息
   */
  getImportStats() {
    return { ...this.importStats };
  }

  /**
   * 重置统计
   */
  resetStats() {
    this.importStats = {
      sentences: 0,
      words: 0,
      idioms: 0,
      errors: 0
    };
  }
}

// 导出单例实例
export const dataImporter = new DataImporter();
