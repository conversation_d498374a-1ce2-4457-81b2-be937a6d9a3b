/**
 * Firebase认证管理器
 * 提供用户注册、登录、登出等功能
 */

import { 
  getAuth, 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  updateProfile,
  sendPasswordResetEmail,
  GoogleAuthProvider,
  signInWithPopup
} from 'firebase/auth';
import { app } from './firebase.js';

// 初始化认证
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();

/**
 * 认证管理器类
 */
export class AuthManager {
  constructor() {
    this.auth = auth;
    this.currentUser = null;
    this.authStateListeners = [];
    
    // 监听认证状态变化
    onAuthStateChanged(this.auth, (user) => {
      this.currentUser = user;
      this.notifyAuthStateListeners(user);
    });
  }

  /**
   * 添加认证状态监听器
   */
  addAuthStateListener(callback) {
    this.authStateListeners.push(callback);
    // 立即调用一次，传递当前状态
    callback(this.currentUser);
  }

  /**
   * 移除认证状态监听器
   */
  removeAuthStateListener(callback) {
    const index = this.authStateListeners.indexOf(callback);
    if (index > -1) {
      this.authStateListeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器认证状态变化
   */
  notifyAuthStateListeners(user) {
    this.authStateListeners.forEach(callback => {
      try {
        callback(user);
      } catch (error) {
        console.error('认证状态监听器错误:', error);
      }
    });
  }

  /**
   * 用户注册
   */
  async register(email, password, displayName = '') {
    try {
      const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
      const user = userCredential.user;
      
      // 更新用户显示名称
      if (displayName) {
        await updateProfile(user, { displayName });
      }
      
      console.log('用户注册成功:', user.uid);
      return {
        success: true,
        user: {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName || displayName
        }
      };
    } catch (error) {
      console.error('用户注册失败:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * 用户登录
   */
  async login(email, password) {
    try {
      const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
      const user = userCredential.user;
      
      console.log('用户登录成功:', user.uid);
      return {
        success: true,
        user: {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName
        }
      };
    } catch (error) {
      console.error('用户登录失败:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * Google登录
   */
  async loginWithGoogle() {
    try {
      const result = await signInWithPopup(this.auth, googleProvider);
      const user = result.user;
      
      console.log('Google登录成功:', user.uid);
      return {
        success: true,
        user: {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          photoURL: user.photoURL
        }
      };
    } catch (error) {
      console.error('Google登录失败:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * 用户登出
   */
  async logout() {
    try {
      await signOut(this.auth);
      console.log('用户登出成功');
      return { success: true };
    } catch (error) {
      console.error('用户登出失败:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * 发送密码重置邮件
   */
  async resetPassword(email) {
    try {
      await sendPasswordResetEmail(this.auth, email);
      console.log('密码重置邮件发送成功');
      return { success: true };
    } catch (error) {
      console.error('密码重置邮件发送失败:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code)
      };
    }
  }

  /**
   * 获取当前用户
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * 检查用户是否已登录
   */
  isLoggedIn() {
    return this.currentUser !== null;
  }

  /**
   * 获取用户友好的错误信息
   */
  getErrorMessage(errorCode) {
    const errorMessages = {
      'auth/user-not-found': '用户不存在',
      'auth/wrong-password': '密码错误',
      'auth/email-already-in-use': '邮箱已被使用',
      'auth/weak-password': '密码强度不够（至少6位）',
      'auth/invalid-email': '邮箱格式无效',
      'auth/user-disabled': '用户账户已被禁用',
      'auth/too-many-requests': '请求过于频繁，请稍后再试',
      'auth/network-request-failed': '网络连接失败',
      'auth/popup-closed-by-user': '登录窗口被用户关闭',
      'auth/cancelled-popup-request': '登录请求被取消'
    };
    
    return errorMessages[errorCode] || '发生未知错误，请稍后再试';
  }
}

// 创建全局认证管理器实例
export const authManager = new AuthManager();

// 导出认证相关的常量和工具函数
export const AUTH_ERRORS = {
  USER_NOT_FOUND: 'auth/user-not-found',
  WRONG_PASSWORD: 'auth/wrong-password',
  EMAIL_IN_USE: 'auth/email-already-in-use',
  WEAK_PASSWORD: 'auth/weak-password',
  INVALID_EMAIL: 'auth/invalid-email'
};

/**
 * 验证邮箱格式
 */
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证密码强度
 */
export const validatePassword = (password) => {
  return password && password.length >= 6;
};
