/**
 * Firestore学习数据管理器
 * 将学习状态管理和学习追踪与Firestore集成
 */

import { databaseAPI } from './firestoreDatabaseAPI.js';
import { LearningStateManager } from './learningStateManager.js';
import { LearningTracker, ACTIVITY_TYPE } from './learningTracker.js';

/**
 * Firestore学习管理器类
 * 整合学习状态管理和数据持久化
 */
export class FirestoreLearningManager {
  constructor() {
    this.stateManager = new LearningStateManager();
    this.tracker = new LearningTracker();
    this.isInitialized = false;
  }

  /**
   * 初始化学习管理器
   */
  async initialize() {
    try {
      // 确保数据库API已初始化
      if (!databaseAPI.isReady) {
        await databaseAPI.initialize();
      }
      
      // 从Firestore加载学习数据
      await this.loadLearningData();
      
      this.isInitialized = true;
      console.log('Firestore学习管理器初始化成功');
      return true;
    } catch (error) {
      console.error('Firestore学习管理器初始化失败:', error);
      return false;
    }
  }

  /**
   * 检查是否已初始化
   */
  checkInitialized() {
    if (!this.isInitialized) {
      throw new Error('学习管理器未初始化，请先调用initialize()');
    }
  }

  /**
   * 从Firestore加载学习数据
   */
  async loadLearningData() {
    try {
      // 加载用户偏好
      const preferences = await databaseAPI.getAllUserPreferences();
      
      // 应用偏好设置到学习状态管理器
      if (preferences.learningSettings) {
        this.stateManager.updateSettings(preferences.learningSettings);
      }
      
      console.log('学习数据加载完成');
    } catch (error) {
      console.warn('加载学习数据失败，使用默认设置:', error);
    }
  }

  /**
   * 记录学习活动
   * @param {Object} activity - 学习活动数据
   * @returns {Promise<string>} 记录ID
   */
  async recordActivity(activity) {
    this.checkInitialized();
    
    try {
      const {
        sentenceId,
        isCorrect,
        responseTime,
        difficulty,
        activityType = ACTIVITY_TYPE.PRACTICE
      } = activity;

      // 计算学习质量和状态
      const quality = this.stateManager.calculateQuality(isCorrect, responseTime, difficulty);
      const learningData = await this.getLearningData(sentenceId);
      
      // 更新学习状态
      const nextInterval = this.stateManager.calculateNextInterval(
        learningData,
        quality,
        difficulty
      );
      
      const memoryStrength = this.stateManager.calculateMemoryStrength(learningData);
      const masteryLevel = this.stateManager.calculateMasteryLevel(learningData);
      
      // 准备学习记录数据
      const recordData = {
        sentenceId,
        isCorrect,
        responseTime,
        masteryLevel,
        memoryStrength,
        nextReview: new Date(Date.now() + nextInterval * 24 * 60 * 60 * 1000),
        correctCount: learningData.correctCount + (isCorrect ? 1 : 0),
        totalAttempts: learningData.totalAttempts + 1
      };

      // 保存到Firestore
      const recordId = await databaseAPI.recordLearningActivity(recordData);
      
      // 更新本地追踪器
      this.tracker.recordActivity({
        sentenceId,
        result: isCorrect ? 'correct' : 'incorrect',
        responseTime,
        difficulty,
        activityType
      });

      return recordId;
    } catch (error) {
      console.error('记录学习活动失败:', error);
      throw error;
    }
  }

  /**
   * 获取句子的学习数据
   * @param {string} sentenceId - 句子ID
   * @returns {Promise<Object>} 学习数据
   */
  async getLearningData(sentenceId) {
    this.checkInitialized();
    
    try {
      const record = await databaseAPI.getLearningRecord(sentenceId);
      
      if (record) {
        return {
          sentenceId: record.sentenceId,
          masteryLevel: record.masteryLevel || 0,
          memoryStrength: record.memoryStrength || 1.0,
          lastReviewed: record.lastReviewed?.toDate?.() || record.lastReviewed,
          nextReview: record.nextReview?.toDate?.() || record.nextReview,
          correctCount: record.correctCount || 0,
          totalAttempts: record.totalAttempts || 0,
          currentInterval: record.currentInterval || 1,
          easinessFactor: record.easinessFactor || 2.5,
          lastQuality: record.lastQuality || 0
        };
      } else {
        // 返回默认学习数据
        return {
          sentenceId,
          masteryLevel: 0,
          memoryStrength: 1.0,
          lastReviewed: null,
          nextReview: null,
          correctCount: 0,
          totalAttempts: 0,
          currentInterval: 1,
          easinessFactor: 2.5,
          lastQuality: 0
        };
      }
    } catch (error) {
      console.error('获取学习数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取需要复习的句子
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 需要复习的句子列表
   */
  async getReviewSentences(limit = 10) {
    this.checkInitialized();
    
    try {
      // 这里需要实现复杂的查询逻辑
      // 由于Firestore的查询限制，我们可能需要获取所有学习记录然后在客户端过滤
      // 在生产环境中，建议使用Cloud Functions来处理这种复杂查询
      
      const allSentences = await databaseAPI.getSentences();
      const reviewSentences = [];
      
      for (const sentence of allSentences) {
        const learningData = await this.getLearningData(sentence.id);
        
        // 检查是否需要复习
        if (this.shouldReview(learningData)) {
          reviewSentences.push({
            ...sentence,
            learningData
          });
        }
        
        if (reviewSentences.length >= limit) {
          break;
        }
      }
      
      // 按优先级排序
      reviewSentences.sort((a, b) => {
        const priorityA = this.calculateReviewPriority(a.learningData);
        const priorityB = this.calculateReviewPriority(b.learningData);
        return priorityB - priorityA;
      });
      
      return reviewSentences;
    } catch (error) {
      console.error('获取复习句子失败:', error);
      throw error;
    }
  }

  /**
   * 判断是否需要复习
   * @param {Object} learningData - 学习数据
   * @returns {boolean} 是否需要复习
   */
  shouldReview(learningData) {
    if (!learningData.nextReview) {
      return learningData.totalAttempts > 0; // 学习过但没有复习计划
    }
    
    return new Date() >= new Date(learningData.nextReview);
  }

  /**
   * 计算复习优先级
   * @param {Object} learningData - 学习数据
   * @returns {number} 优先级分数
   */
  calculateReviewPriority(learningData) {
    const memoryStrength = learningData.memoryStrength || 1.0;
    const masteryLevel = learningData.masteryLevel || 0;
    const daysSinceReview = learningData.lastReviewed 
      ? (new Date() - new Date(learningData.lastReviewed)) / (1000 * 60 * 60 * 24)
      : 0;
    
    // 记忆强度越低，掌握程度越低，距离上次复习时间越长，优先级越高
    return (1 - memoryStrength) * 10 + (5 - masteryLevel) * 2 + daysSinceReview * 0.1;
  }

  /**
   * 开始学习会话
   * @param {string} activityType - 活动类型
   * @param {Object} config - 会话配置
   * @returns {string} 会话ID
   */
  startSession(activityType, config = {}) {
    this.checkInitialized();
    return this.tracker.startSession(activityType, config);
  }

  /**
   * 结束学习会话
   * @returns {Promise<Object>} 会话总结
   */
  async endSession() {
    this.checkInitialized();
    
    const sessionSummary = this.tracker.endSession();
    
    // 将会话数据保存到Firestore（可选）
    try {
      // 这里可以实现会话数据的持久化
      console.log('会话结束:', sessionSummary);
    } catch (error) {
      console.warn('保存会话数据失败:', error);
    }
    
    return sessionSummary;
  }

  /**
   * 获取学习统计
   * @returns {Promise<Object>} 学习统计数据
   */
  async getStatistics() {
    this.checkInitialized();
    
    try {
      // 从Firestore获取统计数据
      const firestoreStats = await databaseAPI.getLearningStatistics();
      
      // 从本地追踪器获取统计数据
      const trackerStats = this.tracker.getStatistics();
      
      // 合并统计数据
      return {
        ...firestoreStats,
        ...trackerStats,
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error('获取学习统计失败:', error);
      throw error;
    }
  }

  /**
   * 保存学习偏好
   * @param {Object} preferences - 偏好设置
   * @returns {Promise<boolean>} 是否保存成功
   */
  async savePreferences(preferences) {
    this.checkInitialized();
    
    try {
      await databaseAPI.setUserPreference('learningSettings', preferences);
      this.stateManager.updateSettings(preferences);
      return true;
    } catch (error) {
      console.error('保存学习偏好失败:', error);
      throw error;
    }
  }

  /**
   * 清除所有学习数据
   */
  async clearAllData() {
    this.checkInitialized();
    
    try {
      // 清除Firestore中的学习记录
      // 注意：这个操作需要谨慎处理，可能需要批量删除
      console.warn('清除学习数据功能需要在生产环境中谨慎实现');
      
      // 清除本地追踪器数据
      this.tracker.clearAllData();
      
      return true;
    } catch (error) {
      console.error('清除学习数据失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const firestoreLearningManager = new FirestoreLearningManager();
