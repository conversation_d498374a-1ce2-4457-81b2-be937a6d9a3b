/**
 * Firestore数据模型定义
 * 定义所有集合的数据结构和验证规则
 */

/**
 * Firestore集合结构设计
 * 
 * 集合层次结构：
 * 
 * /sentences/{sentenceId}                    - 句子集合
 * /users/{userId}                           - 用户集合（未来扩展）
 * /users/{userId}/learningRecords/{recordId} - 用户学习记录子集合
 * /users/{userId}/preferences/{prefId}       - 用户偏好子集合
 * /users/{userId}/sessions/{sessionId}       - 学习会话子集合
 * /words/{wordId}                           - 单词集合
 * /idioms/{idiomId}                         - 俚语集合
 * /globalStats/stats                        - 全局统计（单文档）
 */

// 默认用户ID（用于匿名用户）
export const DEFAULT_USER_ID = 'anonymous';

/**
 * 句子文档结构
 */
export const SentenceSchema = {
  // 基础信息
  english: '',              // 英文句子
  chinese: '',              // 中文翻译
  pronunciation: '',        // 音标
  chinesePronunciation: '', // 中文发音提示
  
  // 分类信息
  scene: '',               // 场景（airport, restaurant, hotel等）
  difficulty: '',          // 难度（beginner, intermediate, advanced）
  tags: [],               // 标签数组
  
  // 详细信息
  explanation: '',         // 详细解释
  grammarPoints: [],      // 语法要点数组
  
  // 元数据
  createdAt: null,        // 创建时间（Timestamp）
  updatedAt: null,        // 更新时间（Timestamp）
  
  // 统计信息（可选，用于优化查询）
  totalAttempts: 0,       // 总尝试次数
  correctAttempts: 0,     // 正确次数
  averageResponseTime: 0  // 平均响应时间
};

/**
 * 学习记录文档结构
 * 路径: /users/{userId}/learningRecords/{recordId}
 */
export const LearningRecordSchema = {
  // 关联信息
  sentenceId: '',         // 句子ID
  
  // 学习状态
  isCorrect: false,       // 是否正确
  responseTime: 0,        // 响应时间（毫秒）
  masteryLevel: 0,        // 掌握程度（0-4）
  memoryStrength: 0,      // 记忆强度（0-1）
  
  // 复习计划
  lastReviewed: null,     // 最后复习时间（Timestamp）
  nextReview: null,       // 下次复习时间（Timestamp）
  
  // 统计信息
  correctCount: 0,        // 正确次数
  totalAttempts: 0,       // 总尝试次数
  
  // 元数据
  createdAt: null,        // 创建时间（Timestamp）
  updatedAt: null         // 更新时间（Timestamp）
};

/**
 * 学习会话文档结构
 * 路径: /users/{userId}/sessions/{sessionId}
 */
export const LearningSessionSchema = {
  // 会话信息
  activityType: '',       // 活动类型（practice, review, challenge等）
  startTime: null,        // 开始时间（Timestamp）
  endTime: null,          // 结束时间（Timestamp）
  
  // 统计信息
  totalQuestions: 0,      // 总题数
  correctAnswers: 0,      // 正确答案数
  accuracy: 0,            // 准确率
  
  // 配置信息
  config: {},             // 会话配置（JSON对象）
  
  // 元数据
  createdAt: null         // 创建时间（Timestamp）
};

/**
 * 用户偏好文档结构
 * 路径: /users/{userId}/preferences/{prefKey}
 */
export const UserPreferenceSchema = {
  key: '',                // 偏好键
  value: null,            // 偏好值（可以是任意类型）
  createdAt: null,        // 创建时间（Timestamp）
  updatedAt: null         // 更新时间（Timestamp）
};

/**
 * 单词文档结构
 */
export const WordSchema = {
  word: '',               // 单词
  pronunciation: '',      // 音标
  partOfSpeech: '',      // 词性
  meaning: '',           // 含义
  usage: '',             // 用法
  exampleSentences: [],  // 例句数组
  frequency: 0,          // 使用频率
  createdAt: null        // 创建时间（Timestamp）
};

/**
 * 俚语文档结构
 */
export const IdiomSchema = {
  english: '',           // 英文俚语
  chinese: '',           // 中文翻译
  pronunciation: '',     // 音标
  meaning: '',           // 含义解释
  usageExample: '',      // 使用例句
  origin: '',            // 来源
  difficulty: '',        // 难度
  tags: [],              // 标签数组
  createdAt: null        // 创建时间（Timestamp）
};

/**
 * 全局统计文档结构
 * 路径: /globalStats/stats
 */
export const GlobalStatsSchema = {
  totalSentences: 0,     // 总句子数
  totalUsers: 0,         // 总用户数
  totalLearningRecords: 0, // 总学习记录数
  averageAccuracy: 0,    // 平均准确率
  updatedAt: null        // 更新时间（Timestamp）
};

/**
 * 集合名称常量
 */
export const COLLECTIONS = {
  SENTENCES: 'sentences',
  USERS: 'users',
  LEARNING_RECORDS: 'learningRecords',
  PREFERENCES: 'preferences',
  SESSIONS: 'sessions',
  WORDS: 'words',
  IDIOMS: 'idioms',
  GLOBAL_STATS: 'globalStats'
};

/**
 * 文档ID常量
 */
export const DOCUMENT_IDS = {
  GLOBAL_STATS: 'stats'
};

/**
 * 验证函数
 */
export const validators = {
  /**
   * 验证句子数据
   */
  validateSentence(data) {
    const required = ['english', 'chinese', 'scene', 'difficulty'];
    for (const field of required) {
      if (!data[field]) {
        throw new Error(`句子数据缺少必需字段: ${field}`);
      }
    }
    return true;
  },

  /**
   * 验证学习记录数据
   */
  validateLearningRecord(data) {
    const required = ['sentenceId', 'isCorrect'];
    for (const field of required) {
      if (data[field] === undefined) {
        throw new Error(`学习记录缺少必需字段: ${field}`);
      }
    }
    return true;
  },

  /**
   * 验证会话数据
   */
  validateSession(data) {
    const required = ['activityType', 'startTime'];
    for (const field of required) {
      if (!data[field]) {
        throw new Error(`会话数据缺少必需字段: ${field}`);
      }
    }
    return true;
  }
};
