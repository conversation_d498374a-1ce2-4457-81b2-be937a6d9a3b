/**
 * Firestore数据库管理器
 * 提供统一的Firestore数据访问接口
 */

import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  serverTimestamp,
  writeBatch,
  runTransaction
} from 'firebase/firestore';

import { db } from './firebase.js';
import { 
  COLLECTIONS, 
  DOCUMENT_IDS, 
  DEFAULT_USER_ID,
  validators 
} from './firestoreSchema.js';

/**
 * Firestore数据库管理器类
 */
export class FirestoreManager {
  constructor() {
    this.db = db;
    this.isInitialized = false;
  }

  /**
   * 初始化数据库
   */
  async initialize() {
    try {
      // 测试连接
      const testDoc = doc(this.db, 'test', 'connection');
      await getDoc(testDoc);
      
      this.isInitialized = true;
      console.log('Firestore数据库初始化成功');
      return true;
    } catch (error) {
      console.error('Firestore数据库初始化失败:', error);
      return false;
    }
  }

  /**
   * 检查是否已初始化
   */
  checkInitialized() {
    if (!this.isInitialized) {
      throw new Error('Firestore数据库未初始化，请先调用initialize()');
    }
  }

  // ==================== 句子相关操作 ====================

  /**
   * 获取所有句子
   * @param {Object} options - 查询选项
   * @returns {Array} 句子数组
   */
  async getAllSentences(options = {}) {
    this.checkInitialized();
    
    try {
      let q = collection(this.db, COLLECTIONS.SENTENCES);
      
      // 添加查询条件
      if (options.scene) {
        q = query(q, where('scene', '==', options.scene));
      }
      if (options.difficulty) {
        q = query(q, where('difficulty', '==', options.difficulty));
      }
      
      // 添加排序
      if (options.orderBy) {
        q = query(q, orderBy(options.orderBy, options.order || 'asc'));
      }
      
      // 添加限制
      if (options.limit) {
        q = query(q, limit(options.limit));
      }
      
      const querySnapshot = await getDocs(q);
      const sentences = [];
      
      querySnapshot.forEach((doc) => {
        sentences.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      return sentences;
    } catch (error) {
      console.error('获取句子失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取句子
   * @param {string} id - 句子ID
   * @returns {Object|null} 句子对象
   */
  async getSentenceById(id) {
    this.checkInitialized();
    
    try {
      const docRef = doc(this.db, COLLECTIONS.SENTENCES, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      } else {
        return null;
      }
    } catch (error) {
      console.error('获取句子失败:', error);
      throw error;
    }
  }

  /**
   * 添加句子
   * @param {Object} sentence - 句子对象
   * @returns {string} 新增句子的ID
   */
  async addSentence(sentence) {
    this.checkInitialized();
    
    try {
      // 验证数据
      validators.validateSentence(sentence);
      
      // 添加时间戳
      const sentenceData = {
        ...sentence,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        totalAttempts: 0,
        correctAttempts: 0,
        averageResponseTime: 0
      };
      
      const docRef = await addDoc(collection(this.db, COLLECTIONS.SENTENCES), sentenceData);
      return docRef.id;
    } catch (error) {
      console.error('添加句子失败:', error);
      throw error;
    }
  }

  /**
   * 更新句子
   * @param {string} id - 句子ID
   * @param {Object} updates - 更新数据
   * @returns {boolean} 是否更新成功
   */
  async updateSentence(id, updates) {
    this.checkInitialized();
    
    try {
      const docRef = doc(this.db, COLLECTIONS.SENTENCES, id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });
      return true;
    } catch (error) {
      console.error('更新句子失败:', error);
      throw error;
    }
  }

  /**
   * 删除句子
   * @param {string} id - 句子ID
   * @returns {boolean} 是否删除成功
   */
  async deleteSentence(id) {
    this.checkInitialized();
    
    try {
      const docRef = doc(this.db, COLLECTIONS.SENTENCES, id);
      await deleteDoc(docRef);
      return true;
    } catch (error) {
      console.error('删除句子失败:', error);
      throw error;
    }
  }

  /**
   * 搜索句子
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Array} 搜索结果
   */
  async searchSentences(keyword, options = {}) {
    this.checkInitialized();
    
    try {
      // Firestore不支持全文搜索，这里使用简单的前缀匹配
      // 在生产环境中，建议使用Algolia或Elasticsearch
      let q = collection(this.db, COLLECTIONS.SENTENCES);
      
      if (keyword) {
        // 搜索英文句子（前缀匹配）
        q = query(q, 
          where('english', '>=', keyword),
          where('english', '<=', keyword + '\uf8ff')
        );
      }
      
      if (options.limit) {
        q = query(q, limit(options.limit));
      }
      
      const querySnapshot = await getDocs(q);
      const sentences = [];
      
      querySnapshot.forEach((doc) => {
        sentences.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      return sentences;
    } catch (error) {
      console.error('搜索句子失败:', error);
      throw error;
    }
  }

  /**
   * 获取随机句子
   * @param {Object} options - 选项
   * @returns {Object|null} 随机句子
   */
  async getRandomSentence(options = {}) {
    this.checkInitialized();
    
    try {
      // Firestore没有内置的随机查询，这里使用一个简单的方法
      // 在生产环境中，可以考虑使用随机字段或其他策略
      let q = collection(this.db, COLLECTIONS.SENTENCES);
      
      if (options.scene) {
        q = query(q, where('scene', '==', options.scene));
      }
      if (options.difficulty) {
        q = query(q, where('difficulty', '==', options.difficulty));
      }
      
      const querySnapshot = await getDocs(q);
      const sentences = [];
      
      querySnapshot.forEach((doc) => {
        sentences.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      if (sentences.length === 0) {
        return null;
      }
      
      // 随机选择一个句子
      const randomIndex = Math.floor(Math.random() * sentences.length);
      return sentences[randomIndex];
    } catch (error) {
      console.error('获取随机句子失败:', error);
      throw error;
    }
  }

  // ==================== 学习记录相关操作 ====================

  /**
   * 记录学习活动
   * @param {Object} record - 学习记录
   * @param {string} userId - 用户ID（可选，默认为匿名用户）
   * @returns {string} 记录ID
   */
  async recordLearningActivity(record, userId = DEFAULT_USER_ID) {
    this.checkInitialized();

    try {
      // 验证数据
      validators.validateLearningRecord(record);

      // 准备记录数据
      const recordData = {
        sentenceId: record.sentenceId,
        isCorrect: record.isCorrect,
        responseTime: record.responseTime || 0,
        masteryLevel: record.masteryLevel || 0,
        memoryStrength: record.memoryStrength || 0,
        lastReviewed: serverTimestamp(),
        nextReview: record.nextReview || null,
        correctCount: record.correctCount || 0,
        totalAttempts: record.totalAttempts || 0,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      const docRef = await addDoc(
        collection(this.db, COLLECTIONS.USERS, userId, COLLECTIONS.LEARNING_RECORDS),
        recordData
      );

      return docRef.id;
    } catch (error) {
      console.error('记录学习活动失败:', error);
      throw error;
    }
  }

  /**
   * 获取句子的学习记录
   * @param {string} sentenceId - 句子ID
   * @param {string} userId - 用户ID（可选，默认为匿名用户）
   * @returns {Object|null} 学习记录
   */
  async getLearningRecord(sentenceId, userId = DEFAULT_USER_ID) {
    this.checkInitialized();

    try {
      const q = query(
        collection(this.db, COLLECTIONS.USERS, userId, COLLECTIONS.LEARNING_RECORDS),
        where('sentenceId', '==', sentenceId),
        orderBy('createdAt', 'desc'),
        limit(1)
      );

      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0];
        return {
          id: doc.id,
          ...doc.data()
        };
      }

      return null;
    } catch (error) {
      console.error('获取学习记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新学习记录
   * @param {string} sentenceId - 句子ID
   * @param {Object} updates - 更新数据
   * @param {string} userId - 用户ID（可选，默认为匿名用户）
   * @returns {boolean} 是否更新成功
   */
  async updateLearningRecord(sentenceId, updates, userId = DEFAULT_USER_ID) {
    this.checkInitialized();

    try {
      // 先查找现有记录
      const existingRecord = await this.getLearningRecord(sentenceId, userId);

      if (existingRecord) {
        // 更新现有记录
        const docRef = doc(
          this.db,
          COLLECTIONS.USERS,
          userId,
          COLLECTIONS.LEARNING_RECORDS,
          existingRecord.id
        );

        await updateDoc(docRef, {
          ...updates,
          updatedAt: serverTimestamp()
        });

        return true;
      } else {
        // 创建新记录
        await this.recordLearningActivity({
          sentenceId,
          ...updates
        }, userId);

        return true;
      }
    } catch (error) {
      console.error('更新学习记录失败:', error);
      throw error;
    }
  }

  // ==================== 用户偏好相关操作 ====================

  /**
   * 获取用户偏好
   * @param {string} key - 偏好键
   * @param {string} userId - 用户ID（可选，默认为匿名用户）
   * @returns {any} 偏好值
   */
  async getUserPreference(key, userId = DEFAULT_USER_ID) {
    this.checkInitialized();

    try {
      const docRef = doc(this.db, COLLECTIONS.USERS, userId, COLLECTIONS.PREFERENCES, key);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return docSnap.data().value;
      }

      return null;
    } catch (error) {
      console.error('获取用户偏好失败:', error);
      throw error;
    }
  }

  /**
   * 设置用户偏好
   * @param {string} key - 偏好键
   * @param {any} value - 偏好值
   * @param {string} userId - 用户ID（可选，默认为匿名用户）
   * @returns {boolean} 是否设置成功
   */
  async setUserPreference(key, value, userId = DEFAULT_USER_ID) {
    this.checkInitialized();

    try {
      const docRef = doc(this.db, COLLECTIONS.USERS, userId, COLLECTIONS.PREFERENCES, key);

      await updateDoc(docRef, {
        key,
        value,
        updatedAt: serverTimestamp()
      }).catch(async () => {
        // 如果文档不存在，创建新文档
        await addDoc(collection(this.db, COLLECTIONS.USERS, userId, COLLECTIONS.PREFERENCES), {
          key,
          value,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      });

      return true;
    } catch (error) {
      console.error('设置用户偏好失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有用户偏好
   * @param {string} userId - 用户ID（可选，默认为匿名用户）
   * @returns {Object} 偏好对象
   */
  async getAllUserPreferences(userId = DEFAULT_USER_ID) {
    this.checkInitialized();

    try {
      const querySnapshot = await getDocs(
        collection(this.db, COLLECTIONS.USERS, userId, COLLECTIONS.PREFERENCES)
      );

      const preferences = {};
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        preferences[data.key] = data.value;
      });

      return preferences;
    } catch (error) {
      console.error('获取所有用户偏好失败:', error);
      throw error;
    }
  }

  // ==================== 学习统计相关操作 ====================

  /**
   * 获取学习统计
   * @param {string} userId - 用户ID（可选，默认为匿名用户）
   * @returns {Object} 统计信息
   */
  async getLearningStatistics(userId = DEFAULT_USER_ID) {
    this.checkInitialized();

    try {
      const stats = {};

      // 获取所有学习记录
      const recordsSnapshot = await getDocs(
        collection(this.db, COLLECTIONS.USERS, userId, COLLECTIONS.LEARNING_RECORDS)
      );

      let totalRecords = 0;
      let correctCount = 0;
      let totalResponseTime = 0;
      const difficultyStats = {};

      recordsSnapshot.forEach((doc) => {
        const record = doc.data();
        totalRecords++;

        if (record.isCorrect) {
          correctCount++;
        }

        if (record.responseTime) {
          totalResponseTime += record.responseTime;
        }
      });

      stats.totalRecords = totalRecords;
      stats.correctCount = correctCount;
      stats.accuracy = totalRecords > 0 ? (correctCount / totalRecords) : 0;
      stats.averageResponseTime = totalRecords > 0 ? (totalResponseTime / totalRecords) : 0;
      stats.byDifficulty = difficultyStats;

      return stats;
    } catch (error) {
      console.error('获取学习统计失败:', error);
      throw error;
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 批量操作
   * @param {Function} operations - 批量操作函数
   * @returns {Promise} 操作结果
   */
  async batchOperation(operations) {
    this.checkInitialized();
    
    const batch = writeBatch(this.db);
    await operations(batch);
    return batch.commit();
  }

  /**
   * 事务操作
   * @param {Function} operations - 事务操作函数
   * @returns {Promise} 操作结果
   */
  async transactionOperation(operations) {
    this.checkInitialized();
    
    return runTransaction(this.db, operations);
  }

  /**
   * 获取数据库统计信息
   * @returns {Object} 统计信息
   */
  async getStats() {
    this.checkInitialized();
    
    try {
      const stats = {};
      
      // 获取句子数量
      const sentencesSnapshot = await getDocs(collection(this.db, COLLECTIONS.SENTENCES));
      stats.sentences = sentencesSnapshot.size;
      
      // 获取单词数量
      const wordsSnapshot = await getDocs(collection(this.db, COLLECTIONS.WORDS));
      stats.words = wordsSnapshot.size;
      
      // 获取俚语数量
      const idiomsSnapshot = await getDocs(collection(this.db, COLLECTIONS.IDIOMS));
      stats.idioms = idiomsSnapshot.size;
      
      // 获取学习记录数量（所有用户）
      // 注意：这个查询可能很慢，在生产环境中应该使用聚合
      let totalLearningRecords = 0;
      const usersSnapshot = await getDocs(collection(this.db, COLLECTIONS.USERS));
      for (const userDoc of usersSnapshot.docs) {
        const recordsSnapshot = await getDocs(
          collection(this.db, COLLECTIONS.USERS, userDoc.id, COLLECTIONS.LEARNING_RECORDS)
        );
        totalLearningRecords += recordsSnapshot.size;
      }
      stats.learning_records = totalLearningRecords;
      
      return stats;
    } catch (error) {
      console.error('获取统计信息失败:', error);
      return {
        sentences: 0,
        words: 0,
        idioms: 0,
        learning_records: 0
      };
    }
  }

  /**
   * 清空所有数据（仅用于开发环境）
   */
  async clearAllData() {
    if (import.meta.env.PROD) {
      throw new Error('生产环境不允许清空数据');
    }
    
    this.checkInitialized();
    
    try {
      const collections = [COLLECTIONS.SENTENCES, COLLECTIONS.WORDS, COLLECTIONS.IDIOMS];
      
      for (const collectionName of collections) {
        const snapshot = await getDocs(collection(this.db, collectionName));
        const batch = writeBatch(this.db);
        
        snapshot.docs.forEach((doc) => {
          batch.delete(doc.ref);
        });
        
        await batch.commit();
      }
      
      console.log('数据库已清空');
    } catch (error) {
      console.error('清空数据库失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const firestoreManager = new FirestoreManager();
